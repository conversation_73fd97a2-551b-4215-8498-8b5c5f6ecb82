name: the_voice_directory_flutter
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  js: ^0.6.7

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  audioplayers:
  device_info_plus: ^11.2.2
  dio:
  equatable:
  firebase_core: ^3.12.0
  flutter_bloc:
  flutter_cache_manager:
  flutter_screenutil:
  flutter_svg:
  get_it:
  image_picker:
  internet_connection_checker:
  intl: ^0.19.0
  jailbreak_root_detection:
  local_auth:
  lottie:
  package_info_plus:
  permission_handler: ^10.0.0
  pin_code_fields: ^8.0.1
  shimmer:
  sms_autofill:
  url_launcher:
  webview_flutter:
  flutter_dotenv:
  flutter_inappwebview:
  country_code_picker:
  dotted_border:
  dotted_line: ^3.2.3
  go_router: ^14.6.3
  file_picker: ^5.2.5
  loading_animation_widget: ^1.3.0
  shared_preferences: ^2.3.5
  dropdown_button2: ^2.3.9
  flutter_native_splash: ^2.3.0
  textfield_tags: 2.0.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  google_nav_bar: ^5.0.7
  universal_html: ^2.2.4
  collection: ^1.18.0
  google_sign_in: ^6.2.2
  cloud_firestore: ^5.6.4
  firebase_storage: ^12.4.3
  firebase_auth: ^5.5.0
  chewie: ^1.10.0
  video_player: ^2.9.3
  onesignal_flutter: ^5.0.4
  app_badge_plus: ^1.2.1
  # googleapis: ^11.4.0 # Client libraries for Google APIs
  # googleapis_auth: ^1.4.1 # Helper for authentication with googleapis
  # http: ^1.1.0 # For making HTTP requests (used by googleapis_auth)
  table_calendar: ^3.1.3
  flutter_typeahead: ^5.2.0  
  sign_in_with_apple: ^7.0.1
  path_provider: ^2.1.5
  widget_zoom: ^0.0.4
  open_file: ^3.5.10
  cached_network_image: ^3.4.1
  expansion_tile_group: ^2.3.0
  carousel_slider: ^5.1.1
  curl_logger_dio_interceptor: ^1.0.0
  just_audio: ^0.9.46

flutter_native_splash:
  color: "#ffffff" # Background color of the splash screen
  image: assets/images/tvd_logo.png # Path to your logo
  android: true # Enable splash screen for Android
  ios: true # Enable splash screen for iOS
  android_gravity: center # Position the logo in the center (Android)
  ios_content_mode: center # Position the logo in the center (iOS)

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  build_runner: ^2.4.0
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/privacy_policy.html

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSans-Black.ttf
          weight: 900
    - family: NotoSans-Bold
      fonts:
        - asset: assets/fonts/NotoSans-Bold.ttf
          weight: 700
    - family: NotoSans-Medium
      fonts:
        - asset: assets/fonts/NotoSans-Medium.ttf
          weight: 500
    - family: NotoSans-Regular
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
          weight: 400
    - family: NotoSans-SemiBold
      fonts:
        - asset: assets/fonts/NotoSans-SemiBold.ttf
          weight: 600
    - family: NotoSansDisplay-Black
      fonts:
        - asset: assets/fonts/NotoSansDisplay-Black.ttf
          weight: 800
    - family: NotoSans-Black
      fonts:
        - asset: assets/fonts/NotoSans-Black.ttf
          weight: 400
    - family: NotoSansDisplay-Italic
      fonts:
        - asset: assets/fonts/NotoSansDisplay-Italic.ttf
          weight: 300
    - family: NotoSans-BlackItalic
      fonts:
        - asset: assets/fonts/NotoSans-BlackItalic.ttf
          weight: 900
    - family: NotoSans-BoldItalic
      fonts:
        - asset: assets/fonts/NotoSans-BoldItalic.ttf
          weight: 900
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
